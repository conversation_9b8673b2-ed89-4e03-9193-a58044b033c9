/**
 * 分页导航显示系统
 * 支持触摸翻页的完整导航信息显示
 */

#ifndef __NAVIGATION_PAGING_H__
#define __NAVIGATION_PAGING_H__

#include "navigation_types.h"
#include "lcd_display_hal.h"

// 全局变量声明
extern NavigationPaging_t g_nav_paging;

// 函数声明
void NavPaging_Init(void);
void NavPaging_LoadDynamicRoute(void);
void NavPaging_LoadIdleState(void);
void NavPaging_UpdateDynamicData(void);
void NavPaging_UpdateCurrentStep(void);
uint8_t NavPaging_GetDirectionFromBearing(float bearing);
float NavPaging_CalculateDistance(float lat1, float lon1, float lat2, float lon2);
uint8_t NavPaging_CalculateCurrentStepFromDistance(float distance_to_dest);

// Simplified function declarations - removed redundant functions
void NavPaging_LoadFallbackRoute(const char* dest_name);

// Detector data processing functions - simplified
uint8_t NavPaging_TryGetDetectorData(void);
uint8_t NavPaging_ParseThingSpeakResponse(const char* response, NavigationDetectorData_t* data);
void NavPaging_LoadDetectorData(NavigationDetectorData_t* data);

// 检测器路线加载函数
void NavPaging_LoadWandaDetectorRoute(NavigationDetectorData_t* data);
void NavPaging_LoadAcademyDetectorRoute(NavigationDetectorData_t* data);
void NavPaging_LoadSportsDetectorRoute(NavigationDetectorData_t* data);
void NavPaging_LoadTrainDetectorRoute(NavigationDetectorData_t* data);
void NavPaging_LoadHospitalDetectorRoute(NavigationDetectorData_t* data);

// Display functions - simplified
void NavPaging_DrawArrow(uint8_t direction, uint16_t x, uint16_t y, uint16_t color);
void NavPaging_NextPage(void);
void NavPaging_PrevPage(void);
void NavPaging_Display(void);
void NavPaging_AutoFlip(void);
void NavPaging_DrawHeader(void);
void NavPaging_DrawSteps(void);
void NavPaging_DrawSingleStep(NavigationStep_t* step, uint16_t y_pos, uint16_t color);
void NavPaging_DrawPageIndicator(void);
uint8_t NavPaging_GetStepsPerPage(void);

// 导航步骤管理
void NavPaging_AddStep(uint8_t step_num, const char* instruction, const char* distance,
                       const char* road_name, uint8_t direction);
void NavPaging_SetCurrentStep(uint8_t step_num);
void NavPaging_CompleteStep(uint8_t step_num);

// 绘制函数
void NavPaging_DrawSingleStep(NavigationStep_t* step, uint16_t y_pos, uint16_t color);
void NavPaging_DrawDirectionArrow(uint8_t direction, uint16_t x, uint16_t y);

#endif /* __NAVIGATION_PAGING_H__ */
