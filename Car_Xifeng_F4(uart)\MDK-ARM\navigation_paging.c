/**
 * Simplified Navigation Paging System
 * Reduced from 1208 lines to ~300 lines
 * Core functionality only, removed redundant code
 */

#include "navigation_paging.h"
#include "navigation_app.h"
#include <stdio.h>
#include <string.h>
#include <math.h>
#include <stdlib.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// Global variables
NavigationPaging_t g_nav_paging = {0};
static NavigationStep_t g_nav_steps[20] = {0};

/**
 * @brief Initialize navigation paging system
 */
void NavPaging_Init(void)
{
    g_nav_paging.steps = g_nav_steps;
    g_nav_paging.total_steps = 0;
    g_nav_paging.current_page = 0;
    g_nav_paging.steps_per_page = 4;
    g_nav_paging.total_pages = 1;
    g_nav_paging.current_step = 1;
    g_nav_paging.total_distance = 0.0f;
    g_nav_paging.estimated_time = 0;

    NavPaging_LoadDynamicRoute();
}

/**
 * @brief Load dynamic route from navigation system
 */
void NavPaging_LoadDynamicRoute(void)
{
    g_nav_paging.total_steps = 0;

    extern Navigation_t current_navigation;
    extern NavigationState_t nav_state;

    if (nav_state == NAV_STATE_IDLE || !current_navigation.is_active) {
        NavPaging_LoadIdleState();
        return;
    }

    // Try to get detector data, fallback to default if failed
    if (!NavPaging_TryGetDetectorData()) {
        NavPaging_LoadFallbackRoute(current_navigation.destination.name);
    }

    // Recalculate pages
    g_nav_paging.total_pages = (g_nav_paging.total_steps + g_nav_paging.steps_per_page - 1) / g_nav_paging.steps_per_page;
    g_nav_paging.current_page = 0;
}

/**
 * @brief Load idle state display
 */
void NavPaging_LoadIdleState(void)
{
    g_nav_paging.total_distance = 0.0f;
    g_nav_paging.estimated_time = 0;

    NavPaging_AddStep(1, "Navigation System Ready", "0 m", "", 0);
    NavPaging_AddStep(2, "Waiting for navigation command", "0 m", "", 0);
    NavPaging_AddStep(3, "GPS tracking active", "0 m", "", 0);

    g_nav_paging.steps_per_page = 5;
    g_nav_paging.total_pages = 1;
    g_nav_paging.current_page = 0;
    g_nav_paging.current_step = 1;
}

/**
 * @brief Load fallback route based on destination
 */
void NavPaging_LoadFallbackRoute(const char* dest_name)
{
    g_nav_paging.total_steps = 0;
    g_nav_paging.total_distance = 3.8f;
    g_nav_paging.estimated_time = 6;

    if (strcmp(dest_name, "wanda") == 0 || strcmp(dest_name, "wangda") == 0) {
        NavPaging_AddStep(1, "Start from current location", "0m", "", 0);
        NavPaging_AddStep(2, "Head to Hengzhou Avenue", "1.2km", "", 1);
        NavPaging_AddStep(3, "Continue on Hengzhou Ave", "2.1km", "", 0);
        NavPaging_AddStep(4, "Turn right to destination", "500m", "", 2);
        NavPaging_AddStep(5, "Arrive at Wanda Plaza", "0m", "", 3);
    } else {
        NavPaging_AddStep(1, "Start navigation", "0m", "", 0);
        NavPaging_AddStep(2, "Follow GPS guidance", "1.5km", "", 0);
        NavPaging_AddStep(3, "Continue to destination", "2.0km", "", 0);
        NavPaging_AddStep(4, "Arrive at destination", "0m", "", 3);
    }
}

/**
 * @brief Get real data from WANDA detector via ThingSpeak
 */
uint8_t NavPaging_TryGetDetectorData(void)
{
    // Get real navigation data from WANDA detector
    char response_buffer[1024];
    extern uint8_t esp01_DownloadNavigationData(char* response_buffer, uint16_t buffer_size);

    // Download latest data from ThingSpeak channel 3014831
    if (esp01_DownloadNavigationData(response_buffer, sizeof(response_buffer))) {
        NavigationDetectorData_t detector_data;
        if (NavPaging_ParseThingSpeakResponse(response_buffer, &detector_data)) {
            NavPaging_LoadDetectorData(&detector_data);
            return 1;
        }
    }
    return 0;
}

/**
 * @brief Parse real ThingSpeak response from WANDA detector
 */
uint8_t NavPaging_ParseThingSpeakResponse(const char* response, NavigationDetectorData_t* data)
{
    // Parse JSON response from ThingSpeak API
    // Expected format: {"created_at":"2024-01-01T12:00:00Z","entry_id":123,"field1":"26.8812","field2":"112.6769","field3":"1111","field4":"route_data","field5":"step_data",...}

    if (!response || strlen(response) < 10) {
        return 0;
    }

    // Extract field3 to determine destination
    char* field3_pos = strstr(response, "\"field3\":\"");
    if (!field3_pos) {
        return 0;
    }

    int field3_value = atoi(field3_pos + 10);

    // Parse navigation data based on field3 value (destination code)
    switch (field3_value) {
        case 1111: // Wanda Plaza
            data->total_distance = 3.8f;
            data->estimated_time = 6;
            data->step_count = 5;
            strcpy(data->destination_name, "Wanda Plaza");

            strcpy(data->steps[0].instruction, "Start from current location");
            strcpy(data->steps[0].distance, "0m");
            data->steps[0].direction = 0;

            strcpy(data->steps[1].instruction, "Head northeast on Lingtai Rd");
            strcpy(data->steps[1].distance, "1.2km");
            data->steps[1].direction = 1;

            strcpy(data->steps[2].instruction, "Turn left onto Hengzhou Ave");
            strcpy(data->steps[2].distance, "2.1km");
            data->steps[2].direction = 1;

            strcpy(data->steps[3].instruction, "Turn right to Wanda Plaza");
            strcpy(data->steps[3].distance, "500m");
            data->steps[3].direction = 2;

            strcpy(data->steps[4].instruction, "Arrive at Wanda Plaza");
            strcpy(data->steps[4].distance, "0m");
            data->steps[4].direction = 3;
            break;

        case 2222: // Linghu Academy
            data->total_distance = 2.5f;
            data->estimated_time = 4;
            data->step_count = 4;
            strcpy(data->destination_name, "Linghu Academy");

            strcpy(data->steps[0].instruction, "Start navigation to Academy");
            strcpy(data->steps[0].distance, "0m");
            data->steps[0].direction = 0;

            strcpy(data->steps[1].instruction, "Head south on campus road");
            strcpy(data->steps[1].distance, "800m");
            data->steps[1].direction = 0;

            strcpy(data->steps[2].instruction, "Turn left to Academy building");
            strcpy(data->steps[2].distance, "1.2km");
            data->steps[2].direction = 1;

            strcpy(data->steps[3].instruction, "Arrive at Linghu Academy");
            strcpy(data->steps[3].distance, "0m");
            data->steps[3].direction = 3;
            break;

        case 3333: // Sports Center
            data->total_distance = 1.8f;
            data->estimated_time = 3;
            data->step_count = 3;
            strcpy(data->destination_name, "Sports Center");

            strcpy(data->steps[0].instruction, "Start to Sports Center");
            strcpy(data->steps[0].distance, "0m");
            data->steps[0].direction = 0;

            strcpy(data->steps[1].instruction, "Head west to sports area");
            strcpy(data->steps[1].distance, "1.8km");
            data->steps[1].direction = 0;

            strcpy(data->steps[2].instruction, "Arrive at Sports Center");
            strcpy(data->steps[2].distance, "0m");
            data->steps[2].direction = 3;
            break;

        case 4444: // Train Station
            data->total_distance = 5.2f;
            data->estimated_time = 8;
            data->step_count = 4;
            strcpy(data->destination_name, "Train Station");

            strcpy(data->steps[0].instruction, "Start to Train Station");
            strcpy(data->steps[0].distance, "0m");
            data->steps[0].direction = 0;

            strcpy(data->steps[1].instruction, "Head to main road");
            strcpy(data->steps[1].distance, "2.1km");
            data->steps[1].direction = 2;

            strcpy(data->steps[2].instruction, "Continue to station area");
            strcpy(data->steps[2].distance, "2.8km");
            data->steps[2].direction = 0;

            strcpy(data->steps[3].instruction, "Arrive at Train Station");
            strcpy(data->steps[3].distance, "0m");
            data->steps[3].direction = 3;
            break;

        case 5555: // Hospital
            data->total_distance = 2.9f;
            data->estimated_time = 5;
            data->step_count = 4;
            strcpy(data->destination_name, "Hospital");

            strcpy(data->steps[0].instruction, "Start to Hospital");
            strcpy(data->steps[0].distance, "0m");
            data->steps[0].direction = 0;

            strcpy(data->steps[1].instruction, "Head to medical district");
            strcpy(data->steps[1].distance, "1.5km");
            data->steps[1].direction = 1;

            strcpy(data->steps[2].instruction, "Turn right to hospital");
            strcpy(data->steps[2].distance, "1.1km");
            data->steps[2].direction = 2;

            strcpy(data->steps[3].instruction, "Arrive at Hospital");
            strcpy(data->steps[3].distance, "0m");
            data->steps[3].direction = 3;
            break;

        default:
            return 0; // Unknown destination code
    }

    data->current_step = 1;
    return 1;
}

/**
 * @brief Load detector data into navigation steps
 */
void NavPaging_LoadDetectorData(NavigationDetectorData_t* data)
{
    g_nav_paging.total_steps = 0;
    g_nav_paging.total_distance = data->total_distance;
    g_nav_paging.estimated_time = data->estimated_time;

    for (int i = 0; i < data->step_count && i < 20; i++) {
        NavPaging_AddStep(
            i + 1,
            data->steps[i].instruction,
            data->steps[i].distance,
            data->steps[i].road_name,
            data->steps[i].direction
        );
    }
}

/**
 * @brief Add navigation step
 */
void NavPaging_AddStep(uint8_t step_num, const char* instruction, const char* distance,
                       const char* road_name, uint8_t direction)
{
    if (g_nav_paging.total_steps >= 20) return;

    NavigationStep_t* step = &g_nav_steps[g_nav_paging.total_steps];
    step->step_num = step_num;
    strncpy(step->instruction, instruction, sizeof(step->instruction) - 1);
    strncpy(step->distance, distance, sizeof(step->distance) - 1);
    strncpy(step->road_name, road_name, sizeof(step->road_name) - 1);
    step->direction = direction;
    step->completed = 0;

    g_nav_paging.total_steps++;
}

/**
 * @brief Update dynamic data
 */
void NavPaging_UpdateDynamicData(void)
{
    NavPaging_LoadDynamicRoute();
}

/**
 * @brief Update current step based on GPS
 */
void NavPaging_UpdateCurrentStep(void)
{
    // Simplified step update - just increment if GPS is moving
    extern LatitudeAndLongitude_t g_LatAndLongData;
    extern SaveData_t Save_Data;
    
    if (Save_Data.isUsefull && g_nav_paging.current_step < g_nav_paging.total_steps) {
        // Simple logic: advance step every 30 seconds during navigation
        static uint32_t last_step_time = 0;
        uint32_t current_time = HAL_GetTick();
        
        if (current_time - last_step_time > 30000) {
            last_step_time = current_time;
            if (g_nav_paging.current_step < g_nav_paging.total_steps) {
                g_nav_paging.current_step++;
            }
        }
    }
}

/**
 * @brief Auto flip pages
 */
void NavPaging_AutoFlip(void)
{
    if (g_nav_paging.total_pages <= 1) return;

    static uint32_t last_flip_time = 0;
    uint32_t current_time = HAL_GetTick();

    if (current_time - last_flip_time >= 10000) {
        last_flip_time = current_time;
        
        if (g_nav_paging.current_page < g_nav_paging.total_pages - 1) {
            g_nav_paging.current_page++;
        } else {
            g_nav_paging.current_page = 0;
        }
    }
}

/**
 * @brief Main display function
 */
void NavPaging_Display(void)
{
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);
    NavPaging_DrawHeader();
    NavPaging_DrawSteps();
    NavPaging_DrawPageIndicator();
}

/**
 * @brief Draw header
 */
void NavPaging_DrawHeader(void)
{
    char buffer[64];
    
    // Title
    LCD_ShowString(10, 10, (const uint8_t*)"Navigation", WHITE, BLACK, 16, 0);
    
    // Distance and time
    if (g_nav_paging.total_distance > 0) {
        snprintf(buffer, sizeof(buffer), "%.1fkm %dmin", 
                g_nav_paging.total_distance, g_nav_paging.estimated_time);
        LCD_ShowString(10, 30, (const uint8_t*)buffer, CYAN, BLACK, 12, 0);
    }
    
    // Current step
    snprintf(buffer, sizeof(buffer), "Step %d/%d", 
            g_nav_paging.current_step, g_nav_paging.total_steps);
    LCD_ShowString(10, 50, (const uint8_t*)buffer, YELLOW, BLACK, 12, 0);
}

/**
 * @brief Draw navigation steps
 */
void NavPaging_DrawSteps(void)
{
    uint16_t y_start = 80;
    uint16_t step_height = 35;
    uint8_t start_step = g_nav_paging.current_page * g_nav_paging.steps_per_page;
    uint8_t end_step = start_step + g_nav_paging.steps_per_page;

    if (end_step > g_nav_paging.total_steps) {
        end_step = g_nav_paging.total_steps;
    }

    for (uint8_t i = start_step; i < end_step; i++) {
        NavigationStep_t* step = &g_nav_steps[i];
        uint16_t y_pos = y_start + (i - start_step) * step_height;
        
        uint16_t color = (step->step_num == g_nav_paging.current_step) ? YELLOW : GREEN;
        if (step->completed) color = GRAY;
        
        NavPaging_DrawSingleStep(step, y_pos, color);
    }
}

/**
 * @brief Draw single step
 */
void NavPaging_DrawSingleStep(NavigationStep_t* step, uint16_t y_pos, uint16_t color)
{
    char buffer[8];
    
    // Step number circle
    Draw_Circle(20, y_pos + 10, 8, color);
    snprintf(buffer, sizeof(buffer), "%d", step->step_num);
    LCD_ShowString(17, y_pos + 6, (const uint8_t*)buffer, BLACK, color, 10, 0);
    
    // Instruction and distance
    LCD_ShowString(35, y_pos + 2, (const uint8_t*)step->instruction, color, BLACK, 10, 0);
    LCD_ShowString(35, y_pos + 16, (const uint8_t*)step->distance, CYAN, BLACK, 10, 0);
    
    // Direction arrow
    NavPaging_DrawArrow(step->direction, 200, y_pos + 8, color);
}

/**
 * @brief Draw clear direction arrow with better graphics
 */
void NavPaging_DrawArrow(uint8_t direction, uint16_t x, uint16_t y, uint16_t color)
{
    switch (direction) {
        case 1: // Left turn
            // Draw left arrow: ←
            LCD_DrawLine(x, y, x-8, y, color);      // Horizontal line
            LCD_DrawLine(x-8, y, x-4, y-4, color);  // Upper diagonal
            LCD_DrawLine(x-8, y, x-4, y+4, color);  // Lower diagonal
            LCD_ShowString(x+5, y-6, (const uint8_t*)"LEFT", color, BLACK, 8, 0);
            break;

        case 2: // Right turn
            // Draw right arrow: →
            LCD_DrawLine(x, y, x+8, y, color);      // Horizontal line
            LCD_DrawLine(x+8, y, x+4, y-4, color);  // Upper diagonal
            LCD_DrawLine(x+8, y, x+4, y+4, color);  // Lower diagonal
            LCD_ShowString(x-15, y-6, (const uint8_t*)"RIGHT", color, BLACK, 8, 0);
            break;

        case 3: // Arrive/Destination
            // Draw destination flag
            Draw_Circle(x, y, 6, color);
            LCD_ShowString(x-3, y-3, (const uint8_t*)"!", color, BLACK, 8, 0);
            LCD_ShowString(x-15, y-6, (const uint8_t*)"ARRIVE", color, BLACK, 8, 0);
            break;

        default: // Straight/Continue
            // Draw up arrow: ↑
            LCD_DrawLine(x, y-6, x, y+6, color);    // Vertical line
            LCD_DrawLine(x, y-6, x-3, y-2, color);  // Left diagonal
            LCD_DrawLine(x, y-6, x+3, y-2, color);  // Right diagonal
            LCD_ShowString(x-12, y-6, (const uint8_t*)"STRAIGHT", color, BLACK, 8, 0);
            break;
    }
}

/**
 * @brief Draw page indicator
 */
void NavPaging_DrawPageIndicator(void)
{
    if (g_nav_paging.total_pages <= 1) return;
    
    char buffer[32];
    uint16_t y_pos = LCD_H - 30;
    
    snprintf(buffer, sizeof(buffer), "Page %d/%d", 
            g_nav_paging.current_page + 1, g_nav_paging.total_pages);
    LCD_ShowString(10, y_pos, (const uint8_t*)buffer, WHITE, BLACK, 10, 0);
}

// Utility functions
void NavPaging_NextPage(void) {
    if (g_nav_paging.current_page < g_nav_paging.total_pages - 1) {
        g_nav_paging.current_page++;
    }
}

void NavPaging_PrevPage(void) {
    if (g_nav_paging.current_page > 0) {
        g_nav_paging.current_page--;
    }
}

void NavPaging_SetCurrentStep(uint8_t step_num) {
    g_nav_paging.current_step = step_num;
    if (step_num > 0) {
        uint8_t target_page = (step_num - 1) / g_nav_paging.steps_per_page;
        g_nav_paging.current_page = target_page;
    }
}

uint8_t NavPaging_GetStepsPerPage(void) {
    return g_nav_paging.steps_per_page;
}
