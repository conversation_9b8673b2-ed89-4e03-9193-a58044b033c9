#include "lcd_init_hal.h"

/**
 * @brief LCD GPIO初始化函数 - 适配HAL库
 */
void LCD_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能GPIO时钟
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_GPIOD_CLK_ENABLE();
    
    // 配置SPI引脚 (PB13-SCK, PB15-MOSI)
    GPIO_InitStruct.Pin = LCD_SCLK_Pin | LCD_MOSI_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
    
    // 配置控制引脚 (PD1-BLK, PD4-RES, PD0-DC)
    GPIO_InitStruct.Pin = LCD_BLK_Pin | LCD_RES_Pin | LCD_DC_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
    
    // 设置初始状态
    HAL_GPIO_WritePin(GPIOB, LCD_SCLK_Pin | LCD_MOSI_Pin, GPIO_PIN_SET);
    HAL_GPIO_WritePin(GPIOD, LCD_BLK_Pin | LCD_RES_Pin | LCD_DC_Pin, GPIO_PIN_SET);
}

/**
 * @brief LCD串行数据写入函数 - 完全按照原始例程实现
 * @param dat 要写入寄存器的数据
 */
void LCD_Writ_Bus(uint8_t dat)
{
    uint8_t i;
    // 注意：原始例程使用CS片选，但你的硬件没有连接CS
    // 所以我们跳过CS控制，直接进行SPI通信

    for(i = 0; i < 8; i++)
    {
        LCD_SCLK_Clr();  // 时钟拉低

        if(dat & 0x80)   // 检查最高位
        {
           LCD_MOSI_Set(); // 数据线拉高
        }
        else
        {
           LCD_MOSI_Clr(); // 数据线拉低
        }

        LCD_SCLK_Set();  // 时钟拉高，数据在上升沿被采样
        dat <<= 1;       // 左移一位，准备下一位
    }
    // 注意：原始例程在这里会设置CS，但我们跳过
}

/**
 * @brief LCD写数据
 * @param dat 写入的数据
 */
void LCD_WR_DATA8(uint8_t dat)
{
    LCD_Writ_Bus(dat);
}

/**
 * @brief LCD写数据
 * @param dat 写入的数据
 */
void LCD_WR_DATA(uint16_t dat)
{
    LCD_Writ_Bus(dat >> 8);
    LCD_Writ_Bus(dat);
}

/**
 * @brief LCD写命令
 * @param dat 写入的命令
 */
void LCD_WR_REG(uint8_t dat)
{
    LCD_DC_Clr(); // 写命令
    LCD_Writ_Bus(dat);
    LCD_DC_Set(); // 写数据
}

/**
 * @brief 设置起始和结束地址
 * @param x1,x2 设置列的起始和结束地址
 * @param y1,y2 设置行的起始和结束地址
 */
void LCD_Address_Set(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2)
{
    // 边界检查
    if(x1 >= LCD_W) x1 = LCD_W - 1;
    if(x2 >= LCD_W) x2 = LCD_W - 1;
    if(y1 >= LCD_H) y1 = LCD_H - 1;
    if(y2 >= LCD_H) y2 = LCD_H - 1;

    // 列地址设置 (CASET)
    LCD_WR_REG(0x2a);
    LCD_WR_DATA8(x1 >> 8);   // 起始列高字节
    LCD_WR_DATA8(x1 & 0xFF); // 起始列低字节
    LCD_WR_DATA8(x2 >> 8);   // 结束列高字节
    LCD_WR_DATA8(x2 & 0xFF); // 结束列低字节

    // 行地址设置 (RASET)
    LCD_WR_REG(0x2b);
    LCD_WR_DATA8(y1 >> 8);   // 起始行高字节
    LCD_WR_DATA8(y1 & 0xFF); // 起始行低字节
    LCD_WR_DATA8(y2 >> 8);   // 结束行高字节
    LCD_WR_DATA8(y2 & 0xFF); // 结束行低字节

    // 内存写入命令 (RAMWR)
    LCD_WR_REG(0x2c);
}

/**
 * @brief LCD初始化函数
 */
void LCD_Init(void)
{
    // LCD initialization - minimal debug output
    LCD_GPIO_Init(); // Initialize GPIO

    // Set initial control signal states
    LCD_DC_Set();    // Data mode
    LCD_SCLK_Set();  // Clock high
    LCD_MOSI_Set();  // Data line high
    LCD_BLK_Clr();   // Turn off backlight first
    HAL_Delay(10);

    // Hardware reset sequence
    LCD_RES_Clr();   // Reset
    HAL_Delay(100);  // Hold reset for 100ms
    LCD_RES_Set();   // Release reset
    HAL_Delay(100);  // Wait for reset completion

    LCD_BLK_Set(); // Turn on backlight
    HAL_Delay(100); // Wait 100ms
    
    // ************* Start Initial Sequence **********//
    // LCD register configuration - removed debug output

    LCD_WR_REG(0x11); // Sleep out
    HAL_Delay(120);   // Delay 120ms

    // ************* Start Initial Sequence **********//
    LCD_WR_REG(0xCF);
    LCD_WR_DATA8(0x00);
    LCD_WR_DATA8(0xD9);
    LCD_WR_DATA8(0X30);
     
    LCD_WR_REG(0xED);  
    LCD_WR_DATA8(0x64); 
    LCD_WR_DATA8(0x03); 
    LCD_WR_DATA8(0X12); 
    LCD_WR_DATA8(0X81); 
     
    LCD_WR_REG(0xE8);  
    LCD_WR_DATA8(0x85); 
    LCD_WR_DATA8(0x10); 
    LCD_WR_DATA8(0x78); 
     
    LCD_WR_REG(0xCB);  
    LCD_WR_DATA8(0x39); 
    LCD_WR_DATA8(0x2C); 
    LCD_WR_DATA8(0x00); 
    LCD_WR_DATA8(0x34); 
    LCD_WR_DATA8(0x02); 
     
    LCD_WR_REG(0xF7);  
    LCD_WR_DATA8(0x20); 
     
    LCD_WR_REG(0xEA);  
    LCD_WR_DATA8(0x00); 
    LCD_WR_DATA8(0x00); 
     
    LCD_WR_REG(0xC0);    // Power control 
    LCD_WR_DATA8(0x21);   // VRH[5:0] 
     
    LCD_WR_REG(0xC1);    // Power control 
    LCD_WR_DATA8(0x12);   // SAP[2:0];BT[3:0] 
     
    LCD_WR_REG(0xC5);    // VCM control 
    LCD_WR_DATA8(0x32); 
    LCD_WR_DATA8(0x3C); 
     
    LCD_WR_REG(0xC7);    // VCM control2 
    LCD_WR_DATA8(0XC1); 
     
    // 内存访问控制 - 这个很重要！
    my_printf(&huart1, "🖼️ 配置显示方向 (USE_HORIZONTAL=%d)...\r\n", USE_HORIZONTAL);
    LCD_WR_REG(0x36);    // Memory Access Control
    if(USE_HORIZONTAL==0)LCD_WR_DATA8(0x08);
    else if(USE_HORIZONTAL==1)LCD_WR_DATA8(0xC8);
    else if(USE_HORIZONTAL==2)LCD_WR_DATA8(0x78);
    else LCD_WR_DATA8(0xA8);
    HAL_Delay(10);  // 添加延时确保设置生效

    // 尝试不同的内存访问控制值 - 有些LCD需要不同的设置
    my_printf(&huart1, "🔄 尝试备用显示方向配置...\r\n");
    LCD_WR_REG(0x36);    // Memory Access Control
    LCD_WR_DATA8(0x00);  // 尝试最基本的设置
    HAL_Delay(10);

    // 像素格式设置 - 16位RGB565
    my_printf(&huart1, "🎨 设置像素格式为RGB565...\r\n");
    LCD_WR_REG(0x3A);
    LCD_WR_DATA8(0x55); // 16bit RGB565
    HAL_Delay(10);

    // 尝试18位RGB666格式 - 有些LCD默认是这个
    my_printf(&huart1, "🎨 尝试RGB666格式...\r\n");
    LCD_WR_REG(0x3A);
    LCD_WR_DATA8(0x66); // 18bit RGB666
    HAL_Delay(10);

    // 再次设置回RGB565
    my_printf(&huart1, "🎨 重新设置RGB565格式...\r\n");
    LCD_WR_REG(0x3A);
    LCD_WR_DATA8(0x55); // 16bit RGB565

    LCD_WR_REG(0xB1);   
    LCD_WR_DATA8(0x00);   
    LCD_WR_DATA8(0x18); 
     
    LCD_WR_REG(0xB6);    // Display Function Control 
    LCD_WR_DATA8(0x0A); 
    LCD_WR_DATA8(0xA2); 
     
    LCD_WR_REG(0xF2);    // 3Gamma Function Disable 
    LCD_WR_DATA8(0x00); 
     
    LCD_WR_REG(0x26);    // Gamma curve selected 
    LCD_WR_DATA8(0x01); 
     
    LCD_WR_REG(0xE0);    // Set Gamma 
    LCD_WR_DATA8(0x0F); 
    LCD_WR_DATA8(0x20); 
    LCD_WR_DATA8(0x1E); 
    LCD_WR_DATA8(0x09); 
    LCD_WR_DATA8(0x12); 
    LCD_WR_DATA8(0x0B); 
    LCD_WR_DATA8(0x50); 
    LCD_WR_DATA8(0XBA); 
    LCD_WR_DATA8(0x44); 
    LCD_WR_DATA8(0x09); 
    LCD_WR_DATA8(0x14); 
    LCD_WR_DATA8(0x05); 
    LCD_WR_DATA8(0x23); 
    LCD_WR_DATA8(0x21); 
    LCD_WR_DATA8(0x00); 
     
    LCD_WR_REG(0XE1);    // Set Gamma 
    LCD_WR_DATA8(0x00); 
    LCD_WR_DATA8(0x19); 
    LCD_WR_DATA8(0x19); 
    LCD_WR_DATA8(0x00); 
    LCD_WR_DATA8(0x12); 
    LCD_WR_DATA8(0x07); 
    LCD_WR_DATA8(0x2D); 
    LCD_WR_DATA8(0x28); 
    LCD_WR_DATA8(0x3F); 
    LCD_WR_DATA8(0x02); 
    LCD_WR_DATA8(0x0A); 
    LCD_WR_DATA8(0x08); 
    LCD_WR_DATA8(0x25); 
    LCD_WR_DATA8(0x2D); 
    LCD_WR_DATA8(0x0F); 
    
    // 开启显示 - 最关键的一步！
    my_printf(&huart1, "🌟 开启LCD显示...\r\n");
    LCD_WR_REG(0x29);    // Display on
    HAL_Delay(50);       // 等待显示稳定

    // Test display inversion
    LCD_WR_REG(0x21);    // Display Inversion On
    HAL_Delay(1000);     // Wait 1 second
    LCD_WR_REG(0x20);    // Display Inversion Off
    HAL_Delay(1000);

    // Fill screen with known color for testing
    LCD_Address_Set(0, 0, LCD_W-1, LCD_H-1);
    for(uint32_t i = 0; i < (uint32_t)LCD_W * LCD_H; i++) {
        LCD_WR_DATA(RED);
    }

    // LCD initialization complete
}
