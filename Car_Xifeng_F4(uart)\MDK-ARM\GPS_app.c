/**
 * GPS应用程序 - 精简版
 * 
 * 功能：
 * 1. GPS数据解析
 * 2. 位置数据管理
 * 3. 简化的数据滤波
 * 
 * 精简版本 - 移除冗余代码，保留核心功能
 */

#include "GPS_app.h"
#include <stdlib.h>

// 全局变量
uint16_t point1 = 0;
float longitude;
float latitude;

SaveData_t Save_Data;

LatitudeAndLongitude_t g_LatAndLongData = {
    .E_W = 0,
    .N_S = 0,
    .latitude = 0.0,
    .longitude = 0.0
};

// GPS data source definitions
#define GPS_SOURCE_REAL     0
#define GPS_SOURCE_FALLBACK 1

// Fallback GPS location (Hengyang Normal University)
#define FALLBACK_LATITUDE   26.881226f
#define FALLBACK_LONGITUDE  112.676903f

char USART_RX_BUF[GPS_BUFFER_LENGTH];
uint8_t uart_GPS_RX_Buff;

extern uint8_t uart3_rx_dma_buffer[UART3_BUFFER_SIZE];
extern uint8_t uart3_ring_buffer_input[UART3_BUFFER_SIZE];
extern struct rt_ringbuffer uart3_ring_buffer;
extern uint8_t uart3_data_buffer[UART3_BUFFER_SIZE];

// GPS滤波器
typedef struct {
    float last_valid_lat;
    float last_valid_lon;
    uint8_t filter_initialized;
    uint8_t position_locked;
    float locked_lat;
    float locked_lon;
} GPSFilter_t;

static GPSFilter_t gps_filter = {0};

void GPS_Init(void)
{
    clrStruct();
    rt_ringbuffer_init(&uart3_ring_buffer, uart3_ring_buffer_input, UART3_BUFFER_SIZE);
    HAL_UARTEx_ReceiveToIdle_DMA(&huart3, uart3_rx_dma_buffer, UART3_BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_usart3_rx, DMA_IT_HT);
}

void GPS_Task(void)
{
    static uint32_t last_gps_time = 0;
    static uint8_t gps_timeout_count = 0;
    uint32_t current_time = HAL_GetTick();

    uint16_t data_len = rt_ringbuffer_data_len(&uart3_ring_buffer);

    if (data_len > 0) {
        rt_ringbuffer_get(&uart3_ring_buffer, uart3_data_buffer, data_len);
        uart3_data_buffer[data_len] = '\0';

        // Process GPS data if available
        if (strstr((char*)uart3_data_buffer, "$GPRMC") != NULL) {
            if (parseGpsBuffer()) {
                last_gps_time = current_time;
                gps_timeout_count = 0;
                Save_Data.gps_source = GPS_SOURCE_REAL;  // Real GPS data
            }
        }

        memset(uart3_data_buffer, 0, data_len);
    }

    // Check GPS timeout (no real GPS data for 10 seconds)
    if (current_time - last_gps_time > 10000) {
        gps_timeout_count++;
        if (gps_timeout_count >= 3) {  // Use fallback after 3 timeouts
            GPS_UseFallbackLocation();
            gps_timeout_count = 0;  // Reset counter
        }
    }
}

void clrStruct(void)
{
    Save_Data.isGetData = 0;
    Save_Data.isParseData = 0;
    Save_Data.isUsefull = 0;
    memset(Save_Data.UTCTime, 0, sizeof(Save_Data.UTCTime));
    memset(Save_Data.latitude, 0, sizeof(Save_Data.latitude));
    memset(Save_Data.N_S, 0, sizeof(Save_Data.N_S));
    memset(Save_Data.longitude, 0, sizeof(Save_Data.longitude));
    memset(Save_Data.E_W, 0, sizeof(Save_Data.E_W));
}

uint8_t parseGpsBuffer(void)
{
    char *subString;
    char *subStringNext;
    char i = 0;
    
    if (Save_Data.isGetData) {
        Save_Data.isParseData = 1;
        
        for (i = 0; i <= 12; i++) {
            if (i == 0) {
                if ((subString = strstr((char*)uart3_data_buffer, "$GPRMC")) == NULL) {
                    Save_Data.isParseData = 0;
                    break;
                }
            } else if (i == 1) {
                subString = strstr(subString, ",");
                if (subString != NULL) {
                    subString++;
                    subStringNext = strstr(subString, ",");
                    if (subStringNext != NULL) {
                        memcpy(Save_Data.UTCTime, subString, subStringNext - subString);
                    }
                }
            } else if (i == 2) {
                subString = strstr(subString, ",");
                if (subString != NULL) {
                    subString++;
                    Save_Data.isUsefull = (*subString == 'A') ? 1 : 0;
                }
            } else if (i == 3) {
                subString = strstr(subString, ",");
                if (subString != NULL) {
                    subString++;
                    subStringNext = strstr(subString, ",");
                    if (subStringNext != NULL) {
                        memcpy(Save_Data.latitude, subString, subStringNext - subString);
                    }
                }
            } else if (i == 4) {
                subString = strstr(subString, ",");
                if (subString != NULL) {
                    subString++;
                    Save_Data.N_S[0] = *subString;
                    Save_Data.N_S[1] = '\0';
                }
            } else if (i == 5) {
                subString = strstr(subString, ",");
                if (subString != NULL) {
                    subString++;
                    subStringNext = strstr(subString, ",");
                    if (subStringNext != NULL) {
                        memcpy(Save_Data.longitude, subString, subStringNext - subString);
                    }
                }
            } else if (i == 6) {
                subString = strstr(subString, ",");
                if (subString != NULL) {
                    subString++;
                    Save_Data.E_W[0] = *subString;
                    Save_Data.E_W[1] = '\0';
                }
            }
        }
        
        // 转换坐标格式
        if (Save_Data.isUsefull) {
            float lat_deg = atof(Save_Data.latitude) / 100.0f;
            float lon_deg = atof(Save_Data.longitude) / 100.0f;
            
            int lat_int = (int)lat_deg;
            int lon_int = (int)lon_deg;
            
            g_LatAndLongData.latitude = lat_int + (lat_deg - lat_int) * 100.0f / 60.0f;
            g_LatAndLongData.longitude = lon_int + (lon_deg - lon_int) * 100.0f / 60.0f;
            g_LatAndLongData.N_S = Save_Data.N_S[0];
            g_LatAndLongData.E_W = Save_Data.E_W[0];
            
            // 更新滤波器
            gps_filter.last_valid_lat = g_LatAndLongData.latitude;
            gps_filter.last_valid_lon = g_LatAndLongData.longitude;
            gps_filter.filter_initialized = 1;

            Save_Data.gps_source = GPS_SOURCE_REAL;  // Mark as real GPS data
            return 1;  // Success
        }
    }
    return 0;  // Failed to parse
}

void printGpsBuffer(void)
{
    if (Save_Data.isUsefull) {
        my_printf(&huart1, "GPS: %.6f°%c, %.6f°%c\r\n",
                  g_LatAndLongData.latitude, g_LatAndLongData.N_S,
                  g_LatAndLongData.longitude, g_LatAndLongData.E_W);
    } else {
        my_printf(&huart1, "GPS signal invalid\r\n");
    }
}

// 位置锁定功能
void GPS_LockPosition(void)
{
    if (gps_filter.filter_initialized && gps_filter.last_valid_lat != 0.0f && gps_filter.last_valid_lon != 0.0f) {
        gps_filter.position_locked = 1;
        gps_filter.locked_lat = gps_filter.last_valid_lat;
        gps_filter.locked_lon = gps_filter.last_valid_lon;
        my_printf(&huart1, "GPS position locked: %.6f°N, %.6f°E\r\n",
                  gps_filter.locked_lat, gps_filter.locked_lon);
    } else {
        my_printf(&huart1, "No valid GPS data, cannot lock position\r\n");
    }
}

void GPS_UnlockPosition(void)
{
    if (gps_filter.position_locked) {
        gps_filter.position_locked = 0;
        my_printf(&huart1, "GPS位置已解锁，恢复实时定位\r\n");
    } else {
        my_printf(&huart1, "GPS位置未锁定\r\n");
    }
}

void GPS_SetLockedPosition(float lat, float lon)
{
    gps_filter.position_locked = 1;
    gps_filter.locked_lat = lat;
    gps_filter.locked_lon = lon;
    my_printf(&huart1, "GPS位置设置并锁定: %.6f°N, %.6f°E\r\n", lat, lon);
}

// 简化的上传功能
void GPS_ManualUpload(void)
{
    my_printf(&huart1, "手动触发GPS上传\r\n");
    
    if (gps_filter.position_locked) {
        my_printf(&huart1, "使用锁定位置: %.6f°N, %.6f°E\r\n", 
                  gps_filter.locked_lat, gps_filter.locked_lon);
    } else if (Save_Data.isUsefull) {
        my_printf(&huart1, "使用实时GPS: %.6f°N, %.6f°E\r\n",
                  g_LatAndLongData.latitude, g_LatAndLongData.longitude);
    } else {
        my_printf(&huart1, "无有效GPS数据\r\n");
    }
}

void GPS_SetCustomLocationAndUpload(float lat, float lon, float alt)
{
    // 更新全局GPS数据
    g_LatAndLongData.latitude = lat;
    g_LatAndLongData.longitude = lon;
    g_LatAndLongData.N_S = 'N';
    g_LatAndLongData.E_W = 'E';
    
    // 标记为有效数据
    Save_Data.isUsefull = 1;
    
    my_printf(&huart1, "设置自定义位置: %.6f°N, %.6f°E, %.1fm\r\n", lat, lon, alt);
    
    // 触发上传
    GPS_ManualUpload();
}

void GPS_PrintUploadStatus(void)
{
    my_printf(&huart1, "=== GPS状态 ===\r\n");
    my_printf(&huart1, "数据有效: %s\r\n", Save_Data.isUsefull ? "是" : "否");
    my_printf(&huart1, "位置锁定: %s\r\n", gps_filter.position_locked ? "是" : "否");
    
    if (gps_filter.position_locked) {
        my_printf(&huart1, "锁定位置: %.6f°N, %.6f°E\r\n", 
                  gps_filter.locked_lat, gps_filter.locked_lon);
    } else if (Save_Data.isUsefull) {
        my_printf(&huart1, "当前位置: %.6f°N, %.6f°E\r\n",
                  g_LatAndLongData.latitude, g_LatAndLongData.longitude);
    }
    my_printf(&huart1, "===============\r\n");
}

// 兼容性函数
void GPS_Test_SimulateData(void)
{
    // Set simulation data - no debug output
    g_LatAndLongData.latitude = 26.8812f;
    g_LatAndLongData.longitude = 112.6769f;
    g_LatAndLongData.N_S = 'N';
    g_LatAndLongData.E_W = 'E';
    Save_Data.isUsefull = 1;
}

/**
 * @brief Use fallback GPS location when real GPS is unavailable
 */
void GPS_UseFallbackLocation(void)
{
    g_LatAndLongData.latitude = FALLBACK_LATITUDE;
    g_LatAndLongData.longitude = FALLBACK_LONGITUDE;
    g_LatAndLongData.N_S = 'N';
    g_LatAndLongData.E_W = 'E';
    Save_Data.isUsefull = 1;
    Save_Data.gps_source = GPS_SOURCE_FALLBACK;
}

/**
 * @brief Check if GPS data is from real GPS or fallback
 */
uint8_t GPS_IsRealData(void)
{
    return (Save_Data.gps_source == GPS_SOURCE_REAL);
}

// 简化的虚拟GPS功能
void GPS_Virtual_Init(void) { /* 简化：不需要复杂的虚拟GPS */ }
void GPS_Virtual_SetLocation(float lat, float lon, float alt) { GPS_SetCustomLocationAndUpload(lat, lon, alt); }
void GPS_Virtual_EnableMovement(uint8_t enable) { /* 简化：不支持移动模拟 */ }
void GPS_Virtual_GenerateData(void) { /* 简化：不需要生成数据 */ }
void GPS_UploadToAMap(void) { GPS_ManualUpload(); }
